<template>
  <div class="dashboard-container">
    <!-- 头部标题 -->
    <div class="header">
      <div class="header-bg">
        <h1 class="title">吴桥县供水综合监管系统</h1>
      </div>
    </div>

    <SmartOperation v-if="activeTab === 'smart'" />
    <PredictionAnalysis v-if="activeTab === 'prediction'" />

    <!-- 底部悬浮tab按钮 -->
    <div class="bottom-tabs floating-tabs">
      <button 
        @click="activeTab = 'smart'" 
        :class="['tab-button', { active: activeTab === 'smart' }]"
      >
        智慧运营
      </button>
      <button 
        @click="activeTab = 'prediction'" 
        :class="['tab-button', { active: activeTab === 'prediction' }]"
      >
        四预分析
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import SmartOperation from '@/components/SmartOperation.vue'
import PredictionAnalysis from '@/components/PredictionAnalysis.vue'

// 响应式数据
const activeTab = ref('smart')

</script>

<style lang="scss" scoped>
/* 全局容器 */
.dashboard-container {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #0f1419 0%, #1a2332 100%);
  color: #ffffff;
  font-family: 'Microsoft YaHei', sans-serif;
  overflow: hidden;
  position: relative;
}

.header {
  width: 100%;
  height: 60px;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 20;
}

.header-bg {
  margin: 0 auto;
  width: 1920px;
  height: 60px;
  box-sizing: border-box;
  background: url('@/assets/screen-header-bg.png') center center / cover no-repeat;
}

.title {
  margin: 0 auto;
  height: 60px;
  line-height: 60px;
  font-size: 28px;
  font-weight: bold;
  color: #fff;
  text-align: center;
}
/* 悬浮底部tab按钮 */
.floating-tabs {
  position: absolute;
  left: 0;
  bottom: 30px;
  width: 100vw;
  display: flex;
  justify-content: center;
  z-index: 20;
  pointer-events: none;
}
.floating-tabs .tab-button {
  pointer-events: auto;
}

.bottom-tabs {
  height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
}

.tab-button {
  background: url('@/assets/tab-btn-def.png') no-repeat 0 0;
  background-size: 100% 100%;
  color: #fff;
  padding: 12px 30px;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  transition: all 0.3s ease;
}

.tab-button.active {
  background: url('@/assets/tab-btn-act.png') no-repeat 0 0;
  background-size: 100% 100%;
  color: #fff;
}

.tab-button:hover:not(.active) {
  // border-color: #00d4ff;
  color: #00d4ff;
}
</style>
