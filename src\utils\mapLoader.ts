class MapLoader {
  private static instance: MapLoader
  private loadPromise: Promise<any> | null = null

  private constructor() {}

  static getInstance(): MapLoader {
    if (!MapLoader.instance) {
      MapLoader.instance = new MapLoader()
    }
    return MapLoader.instance
  }

  loadAMapScript(): Promise<any> {
    if (this.loadPromise) {
      return this.loadPromise
    }

    if (window.AMap) {
      return Promise.resolve(window.AMap)
    }

    this.loadPromise = new Promise((resolve, reject) => {
      const script = document.createElement('script')
      script.type = 'text/javascript'
      script.src = 'https://webapi.amap.com/maps?v=2.0&key=6ff574d8df1f3e3629b6916536482edc&plugin=AMap.ToolBar,AMap.Scale,AMap.HawkEye,AMap.MapType,AMap.Geolocation'
      script.onload = () => {
        resolve(window.AMap)
      }
      script.onerror = reject
      document.head.appendChild(script)
    })

    return this.loadPromise
  }
}

export default MapLoader.getInstance()