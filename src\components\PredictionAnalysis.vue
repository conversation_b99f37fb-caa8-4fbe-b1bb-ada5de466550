<template>
  <div class="map-fullscreen">
    <div ref="predictionMapContainer" class="map"></div>
    <!-- 左侧悬浮面板 -->
    <div class="left-panel floating-panel prediction-panel">
      <!-- 设置模型 -->
      <div class="panel-section ctrl-model">
        <div class="panel-header">
          <span>设置模型</span>
        </div>
        <div class="model-form">
          <div class="form-group">
            <label>模型名称</label>
            <ElInput v-model="modelSettings.name" type="text" placeholder="请输入" class="form-input"></ElInput>
          </div>
          <div class="form-group">
            <label>模型类型</label>
            <ElSelect v-model="modelSettings.type" class="form-select">
              <ElOption value="time-series">时间序列</ElOption>
              <ElOption value="regression">回归分析</ElOption>
            </ElSelect>
          </div>
          <div class="form-group">
            <label>预测目标</label>
            <ElSelect v-model="modelSettings.target" class="form-select">
              <ElOption value="ph">pH</ElOption>
              <ElOption value="turbidity">浊度</ElOption>
            </ElSelect>
          </div>
          <div class="form-group">
            <label>输入特征字段</label>
            <ElSelect v-model="modelSettings.features" class="form-select">
              <ElOption value="ph">pH</ElOption>
              <ElOption value="conductivity">电导率</ElOption>
            </ElSelect>
          </div>
          <div class="form-group">
            <label>时间窗口 (h)</label>
            <ElInput v-model="modelSettings.timeWindow" type="text" placeholder="请输入" class="form-input"></ElInput>
          </div>
          <div class="form-group">
            <label>预测窗口 (h)</label>
            <ElInput v-model="modelSettings.predictionWindow" type="text" placeholder="请输入" class="form-input"></ElInput>
          </div>
          <div class="form-group">
            <label>备注</label>
            <ElInput type="textarea" v-model="modelSettings.notes" placeholder="请输入" class="form-textarea"></ElInput>
          </div>
          <div class="form-actions">
            <button @click="startSimulation" class="btn-secondary">开始模拟</button>
            <button @click="resetForm" class="btn-secondary">取消</button>
            <button class="btn-secondary">查看模拟结果</button>
          </div>
        </div>
      </div>
      <!-- 图例 -->
      <div class="panel-section legend-section">
        <div class="legend-header">
          <span class="legend-title-text">图例</span>
        </div>
        <div class="legend-grid legend-grid-custom">
          <div class="legend-card legend-card-custom" v-for="item in legendItems" :key="item.title">
            <img class="legend-card-icon" src="/src/assets/legend-icon.png" alt="icon" />
            <div class="legend-card-content" style="background:url('/src/assets/legend-bg.png') center center/cover no-repeat;">
              <div class="legend-card-title">{{ item.title }}</div>
              <div class="legend-card-sub">{{ item.unit }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, nextTick, onMounted } from 'vue'
import { ElSelect, ElOption, ElInput } from 'element-plus'
import { startPredictionSimulation } from '@/api/dashboard'
import mapLoader from '@/utils/mapLoader'

let predictionMap:any = null

const initAMap = async () => {
  await mapLoader.loadAMapScript()
  if (predictionMapContainer.value) {
    if (predictionMap) {
      predictionMap.destroy()
      predictionMap = null
    }
    predictionMap = new window.AMap.Map(predictionMapContainer.value, {
      zoom: 12,
      center: [116.8716, 37.6286],
      mapStyle: 'amap://styles/dark',
      resizeEnable: true
    })
  }
}

onMounted(async () => {
  await nextTick()
  await initAMap()
})
const predictionMapContainer = ref(null)
const modelSettings = ref({
  name: '',
  type: 'time-series',
  target: 'ph',
  features: 'ph',
  timeWindow: '',
  predictionWindow: '',
  notes: ''
})
const legendItems = [
  { title: 'pH', unit: '无量纲' },
  { title: '电导率', unit: 'μS/cm' },
  { title: '浊度', unit: 'NTU' },
  { title: '溶解氧', unit: 'mg/L' },
  { title: '余氯', unit: 'mg/L' },
  { title: '水温', unit: '℃' }
]
const startSimulation = async () => {
  try {
    const data = await startPredictionSimulation(modelSettings.value)
    alert('模拟开始，数据将在地图上显示')
  } catch (error) {
    console.error('模拟失败:', error)
  }
}
const resetForm = () => {
  modelSettings.value = {
    name: '',
    type: 'time-series',
    target: 'ph',
    features: 'ph',
    timeWindow: '',
    predictionWindow: '',
    notes: ''
  }
}
</script>
<style lang="scss" scoped>
:deep(.el-select__wrapper) {
  background-color: rgba(255, 255, 255, 0.1);
  box-shadow: none;
}
:deep(.el-select__wrapper:hover) {
  box-shadow: none;
}
:deep(.el-select__placeholder) {
  color: #fff;
}
:deep(.el-input__wrapper) {
  background-color: rgba(255, 255, 255, 0.1);
  box-shadow: none;
}
:deep(.el-input__inner) {
  color: #fff;
}
:deep(.el-textarea__inner) {
  background-color: rgba(255, 255, 255, 0.1);
  box-shadow: none;
}
:deep(.el-textarea__inner) {
  color: #fff;
}
.map-fullscreen {
  position: absolute;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1;
}
.map-fullscreen .map {
  width: 100vw;
  height: 100vh;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
}
/* 悬浮面板样式 */
.floating-panel {
  position: absolute;
  top: 80px;
  z-index: 10;
  background: none;
  pointer-events: none;
}
.left-panel.floating-panel {
  left: 30px;
  width: 520px;
  pointer-events: none;
}
.floating-panel .panel-section {
  pointer-events: auto;
}
.panel-section {
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
  padding: 15px;
  width: 520px;
  height: 298px;
  box-sizing: border-box;
  background: url('@/assets/chart-bg.png') no-repeat 0 0;
  background-size: 100% 100%;
}
.ctrl-model {
  padding: 25px 15px;
  height: auto;
  background: url('@/assets/ctrl-bg.png') no-repeat 0 0;
  background-size: 100% 100%;
}
.panel-header {
  color: #00d4ff;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
  text-align: center;
}
.model-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
}
.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}
.form-group label {
  color: #fff;
  font-size: 14px;
}
.form-select, .form-input, .form-textarea {
  width: 100%;
}
.form-input::placeholder {
  color: #fff;
}
.form-textarea {
  resize: vertical;
  min-height: 60px;
}
.form-actions {
  display: flex;
  justify-content: space-around;
  // gap: 8px;
  // flex-wrap: wrap;
}
.btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid #1890ff;
  color: #fff;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}
.legend-section {
  box-shadow: none;
}
.legend-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  position: relative;
}
.legend-title-text {
  color: #fff;
  font-size: 20px;
  font-weight: bold;
  letter-spacing: 8px;
}
.legend-grid-custom {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 18px 24px;
  padding: 0 10px 10px 10px;
}
.legend-card-custom {
  display: flex;
  align-items: center;
  gap: 10px;
  background: none;
  border-radius: 8px;
  box-shadow: none;
  padding: 0;
}
.legend-card-icon {
  width: 48px;
  height: 48px;
  display: block;
}
.legend-card-content {
  flex: 1;
  min-width: 120px;
  min-height: 48px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  border-radius: 6px;
  padding: 4px 12px;
  box-sizing: border-box;
}
.legend-card-title {
  color: #fff;
  font-size: 16px;
  font-weight: bold;
  letter-spacing: 2px;
  margin-bottom: 2px;
}
.legend-card-sub {
  color: #00d4ff;
  font-size: 13px;
  font-weight: normal;
}
</style>


