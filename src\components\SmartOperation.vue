<!-- 智慧运营地图全屏层 -->
<template>
  <div class="map-fullscreen">
    <div ref="mapContainer" class="map"></div>
    <!-- 左侧悬浮面板 -->
    <div class="left-panel floating-panel">
      <div class="panel-section">
        <div class="panel-header">综合态势</div>
        <div class="stats-container">
          <div class="stats-grid">
            <div class="stat-item">
              <div class="stat-icon water-icon"></div>
              <div class="stat-content">
                <div class="stat-label">昨日用水量</div>
                <div class="stat-value">{{ statsData.yesterdayWater }}</div>
                <span class="unit">吨</span>
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-icon rate-icon"></div>
              <div class="stat-content">
                <div class="stat-label">水质合格率</div>
                <div class="stat-value">{{ statsData.waterQualityRate }}</div>
                <span class="unit">%</span>
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-icon station-icon"></div>
              <div class="stat-content">
                <div class="stat-label">配水站数量</div>
                <div class="stat-value">{{ statsData.stationCount }}</div>
                <span class="unit">个</span>
              </div>
            </div>
          </div>
          <div class="equipment-stats">
            <div class="equipment-item">
              <!-- <div class="equipment-icon"></div> -->
              <div class="equipment-info">
                <div class="equipment-label">设备数量</div>
                <div class="equipment-value">{{ statsData.equipmentCount }}</div>
              </div>
            </div>
            <div class="status-bars">
              <div class="status-item">
                <div class="status-labels">
                  <span class="status-label">在线</span>
                  <p>
                    <span class="status-percent online">{{ statsData.onlinePercent }}</span>
                    <span>%</span>
                  </p>
                </div>
                <div class="status-bar">
                  <div class="status-fill online" :style="{ width: statsData.onlinePercent + '%' }"></div>
                </div>
              </div>
              <div class="status-item">
                <div class="status-labels">
                  <span class="status-label">离线</span>
                  <p>
                    <span class="status-percent offline">{{ statsData.offlinePercent }}</span>
                    <span>%</span>
                  </p>
                </div>
                <div class="status-bar">
                  <div class="status-fill offline" :style="{ width: statsData.offlinePercent + '%' }"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="panel-section">
        <div class="panel-header">水质告警</div>
        <div class="alert-table">
          <div class="alert-header">
            <span>站点名称</span>
            <span>告警参数</span>
            <span>参数值</span>
            <span>开始时间</span>
            <span>操作</span>
          </div>
          <div class="scroll-view">
            <div class="alert-body" :style="alertBodyStyle">
              <div class="alert-row" v-for="(item, idx) in alertRowsDisplay" :key="idx">
                <span>{{ item.site }}</span>
                <span>{{ item.param }}</span>
                <span>{{ item.value }}</span>
                <span>{{ item.time }}</span>
                <span class="alert-action">查看</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="panel-section work-order-section">
        <div class="panel-header">工单处置</div>
        <div class="work-order-tabs">
          <button :class="['tab-btn', {'active':activeOrderTab === 0}]" @click="onChangeOrderTab(0)">昨日</button>
          <button :class="['tab-btn', {'active':activeOrderTab === 1}]" @click="onChangeOrderTab(1)">本月</button>
          <button :class="['tab-btn', {'active':activeOrderTab === 2}]" @click="onChangeOrderTab(2)">今年</button>
        </div>
        <div class="work-order-main">
          <div class="work-order-chart-area">
            <div ref="workOrderChart" class="work-order-chart-ring"></div>
          </div>
          <div class="work-order-indicators">
            <div class="work-order-indicator">
              <div class="work-order-indicator-content">
                <div class="work-order-indicator-label">办结率</div>
                <div class="work-order-indicator-value">{{ workOrderCompletionRate }}</div>
                <span class="work-order-indicator-unit">%</span>
              </div>
            </div>
            <div class="work-order-indicator">
              <div class="work-order-indicator-content">
                <div class="work-order-indicator-label">待处理</div>
                <div class="work-order-indicator-value">{{ workOrderData[0] ? workOrderData[0].value : 0 }}</div>
                <span class="work-order-indicator-unit">件</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 右侧悬浮面板 -->
    <div class="right-panel floating-panel">
      <div class="panel-section">
        <div class="panel-header">实时监测</div>
        <div class="monitoring-controls">
          <el-select v-model="stationValue" class="monitor-select" @change="onStationChange">
            <el-option v-for="item in waterStationList" :key="item.stationId" :label="item.stationName" :value="item.stationId" />
          </el-select>
          <div>
            <button :class="['monitor-btn', { 'active': activeMonitorType === 'flow' }]" @click="onMonitorTypeChange('flow')">流量</button>
            <button :class="['monitor-btn', { 'active': activeMonitorType === 'pressure' }]" @click="onMonitorTypeChange('pressure')">压力</button>
          </div>
        </div>
        <div class="monitoring-chart">
          <div ref="monitoringChart" style="width: 100%; height: 200px;"></div>
        </div>
      </div>
      <div class="panel-section">
        <div class="panel-header">营收情况</div>
        <div class="revenue-tabs">
          <button :class="['tab-btn', { 'active': activeRevenueTab === 1 }]" @click="onRevenueTabClick(1)">昨日</button>
          <button :class="['tab-btn', { 'active': activeRevenueTab === 0 }]" @click="onRevenueTabClick(0)">今日</button>
        </div>
        <div class="revenue-grid">
          <div class="revenue-item">
            <div class="revenue-icon income"></div>
            <div class="revenue-info">
              <div class="revenue-label">总收入</div>
              <span class="revenue-value">{{ revenueData.totalWaterFee }}</span>
              <span>万</span>
            </div>
          </div>
          <div class="revenue-item">
            <div class="revenue-icon outcome"></div>
            <div class="revenue-info">
              <div class="revenue-label">总支出</div>
              <span class="revenue-value">{{ revenueData.totalExpense }}</span>
              <span>万</span>
            </div>
          </div>
          <div class="revenue-item">
            <div class="revenue-icon profit"></div>
            <div class="revenue-info">
              <div class="revenue-label">净利润</div>
              <span class="revenue-value">{{ revenueData.totalProfit }}</span>
              <span>万</span>
            </div>
          </div>
          <div class="revenue-item">
            <div class="revenue-icon rate"></div>
            <div class="revenue-info">
              <div class="revenue-label">收费率</div>
              <span class="revenue-value">{{ revenueData.chargeRate }}</span>
              <span>%</span>
            </div>
          </div>
        </div>
      </div>

      <div class="panel-section">
        <div class="panel-header">基础统计</div>
        <div class="basic-stats">
          <div class="basic-stat-row">
            <div class="basic-stat-item">
              <div class="basic-stat-icon water-quality"></div>
              <div class="basic-stat-info">
                <div>
                  <div class="basic-stat-label">水质传感器</div>
                  <p>
                    <span class="master">{{ sensorCount }}</span>
                    <span>个</span>
                  </p>
                </div>
                <div>
                  <div class="basic-stat-label">在线</div>
                  <p>
                    <span class="online">{{ sensorOnlineCount }}</span>
                    <span>个</span>
                  </p>
                </div>
                  <div>
                  <div class="basic-stat-label">离线</div>
                  <p>
                    <span class="offline">{{ sensorOfflineCount }}</span>
                    <span>个</span>
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div class="basic-stat-row">
            <div class="basic-stat-item">
              <div class="basic-stat-icon flow"></div>
              <div class="basic-stat-info">
                <div>
                  <div class="basic-stat-label">流量计</div>
                  <p>
                    <span class="master">{{ flowmeterCount }}</span>
                    <span>个</span>
                  </p>
                </div>
                <div>
                  <div class="basic-stat-label">在线</div>
                  <p>
                    <span class="online">{{ flowmeterOnlineCount }}</span>
                    <span>个</span>
                  </p>
                </div>
                  <div>
                  <div class="basic-stat-label">离线</div>
                  <p>
                    <span class="offline">{{ flowmeterOfflineCount }}</span>
                    <span>个</span>
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div class="basic-stat-row">
            <div class="basic-stat-item">
              <div class="basic-stat-icon pressure"></div>
              <div class="basic-stat-info">
                <div>
                  <div class="basic-stat-label">压力计</div>
                  <p>
                    <span class="master">{{ pressureCount }}</span>
                    <span>个</span>
                  </p>
                </div>
                <div>
                  <div class="basic-stat-label">在线</div>
                  <p>
                    <span class="online">{{ pressureOnlineCount }}</span>
                    <span>个</span>
                  </p>
                </div>
                  <div>
                  <div class="basic-stat-label">离线</div>
                  <p>
                    <span class="offline">{{ pressureOfflineCount }}</span>
                    <span>个</span>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted, nextTick, onUnmounted, computed } from 'vue'
import * as echarts from 'echarts'
import dayjs from 'dayjs'
import { ElSelect, ElOption } from 'element-plus'
import {
  getComprehensiveSituation,
  getWaterQualityAlerts,
  getWorkOrderData,
  getBasicStatistics,
  getWaterStationList,
  getRevenueData,
  getDeviceList,
  getFlowData,
  getPressureData
} from '@/api/dashboard'
import mapLoader from '@/utils/mapLoader'

// 响应式数据
const mapContainer = ref(null)
const workOrderChart = ref(null)
const monitoringChart = ref(null)
// 综合态势数据
const statsData = ref({
  yesterdayWater: 0,
  waterQualityRate: 0,
  stationCount: 0,
  equipmentCount: 0,
  onlinePercent: '0',
  offlinePercent: '0'
})
// 水质告警滚动数据与逻辑
const alertRows = ref([])
const alertScrollIndex = ref(0)
let alertScrollTimer:any = null

// 显示行数和行高
const VISIBLE_ROW_COUNT = 4
const ROW_HEIGHT = 50
// 用于无缝滚动的显示数据，末尾拼接前VISIBLE_ROW_COUNT行
const alertRowsDisplay = computed(() => {
  return [...alertRows.value, ...alertRows.value.slice(0, VISIBLE_ROW_COUNT)]
})

// 记录是否处于无动画跳转（用于无缝滚动）
const isJumping = ref(false)
// 工单
const activeOrderTab = ref(0)
const workOrderDate = ref('')
const workOrderData = ref([])
// 基础统计
const sensorCount = ref(0)
const sensorOnlineCount = ref(0)
const sensorOfflineCount = ref(0)
const flowmeterCount = ref(0)
const flowmeterOnlineCount = ref(0)
const flowmeterOfflineCount = ref(0)
const pressureCount = ref(0)
const pressureOnlineCount = ref(0)
const pressureOfflineCount = ref(0)
// 实时监测
const activeMonitorType = ref('flow')
const waterStationList = ref([])
const stationValue = ref('')
const flowDeviceCode = ref('')
const pressureDeviceCode = ref('')
const sensorDeviceCode = ref('')
const flowData = ref([])
const pressureData = ref([])
// 营收情况
const activeRevenueTab = ref(0)
const revenueDate = ref(dayjs().format('YYYY-MM-DD'))
const revenueData = ref({
  totalWaterFee: '-',
  totalExpense: '-',
  totalProfit: '-',
  chargeRate: '-'
})

const alertBodyStyle = computed(() => {
  return {
    transform: `translateY(-${alertScrollIndex.value * ROW_HEIGHT}px)`,
    transition: isJumping.value ? 'none' : 'transform 0.5s ease-out'
  }
})
// 工单完成率
const workOrderCompletionRate = computed(() => {
  if (workOrderData.value.length === 0 || workOrderData.value[2].value === 0) return '0'
  return (workOrderData.value[2].value / (workOrderData.value[0].value + workOrderData.value[1].value + workOrderData.value[2].value) * 100).toFixed(1)
})
// 地图实例
let smartMap:any = null
// 初始化高德地图
const initAMap = async () => {
  await mapLoader.loadAMapScript()
  if (mapContainer.value) {
    if (smartMap) {
      smartMap.destroy()
      smartMap = null
    }
    smartMap = new window.AMap.Map(mapContainer.value, {
      zoom: 12,
      center: [116.8716, 37.6286],
      mapStyle: 'amap://styles/dark',
      resizeEnable: true
    })
  }
}

// 加载综合态势数据
const loadStatsData = async () => {
  try {
    const res = await getComprehensiveSituation()
    const d = res.data || {}
    statsData.value = {
      yesterdayWater: d.yesterdayWater || 0,
      waterQualityRate: d.waterQualityRate || 0,
      stationCount: d.stationCount || 0,
      equipmentCount: d.meterCount || 0,
      onlinePercent: (d.onlineMeterCount / d.meterCount * 100).toFixed(1) || '0',
      offlinePercent: ((d.meterCount - d.onlineMeterCount) / d.meterCount * 100).toFixed(1) || '0'
    }
  } catch (e) {
    console.error('综合态势数据获取失败', e)
  }
}

// 加载水质告警数据
const loadAlertRows = async () => {
  try {
    const res = await getWaterQualityAlerts()
    const list = res.rows || []
    alertRows.value = list.map(item => ({
      site: item.stationName || '-',
      param: item.paramName || '-',
      value: item.paramValue || '-',
      time: item.startTime || '-'
    }))
  } catch (e) {
    console.error('水质告警数据获取失败', e)
  }
}

function startAlertScroll() {
  if (alertScrollTimer) clearInterval(alertScrollTimer)
  alertScrollTimer = setInterval(() => {
    // 到达补充的最后一行时，先平滑滚动到补充行，再瞬间跳回首行
    if (alertScrollIndex.value < alertRows.value.length) {
      alertScrollIndex.value++
      isJumping.value = false
    } else {
      // 先平滑到补充行，等动画结束后瞬间跳回首行
      isJumping.value = false
      alertScrollIndex.value++
      setTimeout(() => {
        isJumping.value = true
        alertScrollIndex.value = 0
      }, 500) // 500ms为transition动画时长
    }
  }, 2000)
}

// 初始化监测图表
const initMonitoringChart = () => {
  if (!monitoringChart.value) return
  
  // 销毁现有图表实例
  const existingChart = echarts.getInstanceByDom(monitoringChart.value)
  if (existingChart) {
    existingChart.dispose()
  }
  
  const chart = echarts.init(monitoringChart.value)
  
  // 根据当前类型获取对应数据
  const currentData = activeMonitorType.value === 'flow' ? flowData.value : pressureData.value
  
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    grid: {
      left: '3%',
      right: '4%', 
      bottom: '3%',
      containLabel: true
    },
    legend: {
      top: 0,
      left: 'center',
      textStyle: {
        color: '#fff',
        fontSize: '12px'
      }
    },
    xAxis: {
      type: 'category',
      data: currentData.map(item => dayjs(item.monitorTime).format('MM-DD HH')),
      axisLine: { lineStyle: { color: '#00d4ff' } },
      axisLabel: { color: '#00d4ff' }
    },
    yAxis: {
      type: 'value',
      axisLine: { lineStyle: { color: '#00d4ff' } },
      axisLabel: { color: '#00d4ff' },
      splitLine: { lineStyle: { color: '#2a3441' } }
    },
    series: activeMonitorType.value === 'flow' ? [
      {
        name: '瞬时流量',
        type: 'line',
        data: currentData.map(item => item.flowValue),
        lineStyle: { color: '#6bcf7f' },
        itemStyle: { color: '#6bcf7f' }
      },
      {
        name: '累计流量', 
        type: 'line',
        data: currentData.map(item => item.totalFlow),
        lineStyle: { color: '#00d4ff' },
        itemStyle: { color: '#00d4ff' }
      }
    ] : [
      {
        name: '压力值',
        type: 'line', 
        data: currentData.map(item => item.pressureValue),
        lineStyle: { color: '#6bcf7f' },
        itemStyle: { color: '#6bcf7f' }
      }
    ]
  }
  chart.setOption(option)
}

// 初始化工单处置图表
const initWorkOrderChart = () => {
  if (!workOrderChart.value) return
  
  const chart = echarts.init(workOrderChart.value)
  const option = {
    tooltip: {
      trigger: 'item'
    },
    // 显示图例
     legend: {
      bottom: 0,
      left: 'center',
      textStyle: {
        color: '#fff',
        fontSize: '12px'
      }
    },
    series: [{
      type: 'pie',
      radius: ['40%', '70%'],
      center: ['50%', '40%'],
      data: workOrderData.value.map(item => ({
        name: item.name,
        value: item.value,
        itemStyle: {
          color: item.name === '待处理' ? '#ff6b6b' : item.name === '处理中' ? '#ffd93d' : '#6bcf7f'
        }
      })),
      label: {
        show: false
      }
    }]
  }
  chart.setOption(option)
}
// 切换工单数据范围
const onChangeOrderTab = (index: number) => {
  activeOrderTab.value = index
  // 0-昨日 1-本月 2-今年
  let days = 1
  if (index === 1) {
    days = 30
  } else if (index === 2) {
    days = 365
  }
  workOrderDate.value = dayjs().subtract(days, 'day').format('YYYY-MM-DD')
  loadWorkOrderData()
}
// 请求工单数据
const loadWorkOrderData = async () => {
  // status 0-待处理 1-处理中 2-已完成
  try {
    const r1 = await getWorkOrderData({
      createTime: workOrderDate.value,
      status: 0 // 工单类型-待处理
    })
    const r2 = await getWorkOrderData({
      createTime: workOrderDate.value,
      status: 1 // 工单类型-处理中
    })
    const r3 = await getWorkOrderData({
      createTime: workOrderDate.value,
      status: 2 // 工单类型-已完成
    })
    workOrderData.value = [
      { name: '待处理', value: r1.total || 0 },
      { name: '处理中', value: r2.total || 0 },
      { name: '已完成', value: r3.total || 0 }
    ]
    initWorkOrderChart()
  } catch (e) {
    console.error('工单数据获取失败', e)
  }
}
// 实时监测
// 切换水站
const onStationChange = (value: string) => {
  stationValue.value = value
  loadDeviceList()
}
// 切换流量/压力
const onMonitorTypeChange = (type: string) => {
  activeMonitorType.value = type
  // 先更新图表，再加载数据
  initMonitoringChart()
  if (type === 'flow') {
    loadFlowData()
  } else {
    loadPressureData()
  }
}
// 获取水站列表
const loadWaterStationList = async () => {
  try {
    const res = await getWaterStationList()
    waterStationList.value = res.rows || []
    stationValue.value = waterStationList.value[0]?.stationId || ''
    loadDeviceList()
  } catch (e) {
    console.error('水站列表获取失败', e)
  }
}
// 根据所选水站获取设备
const loadDeviceList = async () => {
  try {
    const res = await getDeviceList({
      stationId: stationValue.value
    })
    const list = res.rows || []
    // deviceType：flowmeter 流量设备，pressuremeter 压力设备
    // 分别从rows内获取第一个流量设备和压力设备的deviceCode
    const flowDevice = list.find(item => item.deviceType === 'flowmeter')
    const pressureDevice = list.find(item => item.deviceType === 'pressuremeter')
    const sensorDevice = list.find(item => item.deviceType === 'sensor')
    flowDeviceCode.value = flowDevice?.deviceCode || ''
    pressureDeviceCode.value = pressureDevice?.deviceCode || '';
    sensorDeviceCode.value = sensorDevice?.deviceCode || ''
    (activeMonitorType.value === 'flow' ? loadFlowData() : loadPressureData())
  } catch (e) {
    console.error('监测设备获取失败', e)
  }
}
// 获取流量数据
const loadFlowData = async () => {
  try {
    const startTime = dayjs().subtract(1, 'day').format('YYYY-MM-DD 00:00:00')
    const endTime = dayjs().format('YYYY-MM-DD 23:59:59')
    const res = await getFlowData({ startTime, endTime, deviceCode: flowDeviceCode.value })
    flowData.value = res.data.data || []
    initMonitoringChart()
  } catch (e) {
    console.error('流量数据获取失败', e)
  }
}
// 获取压力数据
const loadPressureData = async () => {
  try {
    const startTime = dayjs().subtract(1, 'day').format('YYYY-MM-DD 00:00:00')
    const endTime = dayjs().format('YYYY-MM-DD 23:59:59')
    const res = await getPressureData({ startTime, endTime, deviceCode: pressureDeviceCode.value })
    pressureData.value = res.data.data || []
    initMonitoringChart()
  } catch (e) {
    console.error('压力数据获取失败', e)
  }
}
// 营收情况
const onRevenueTabClick = (index: number) => {
  activeRevenueTab.value = index
  revenueDate.value = dayjs().subtract(index, 'day').format('YYYY-MM-DD')
  loadRevenueData()
}
const loadRevenueData = async () => {
  try {
    const startTime = revenueDate.value + ' 00:00:00'
    const endTime = revenueDate.value + ' 23:59:59'
    const res = await getRevenueData({ startTime, endTime })
    revenueData.value = res.data || {
      totalWaterFee: '-',
      totalExpense: '-',
      totalProfit: '-',
      chargeRate: '-'
    }
  } catch (e) {
    revenueData.value = {
      totalWaterFee: '-',
      totalExpense: '-',
      totalProfit: '-',
      chargeRate: '-'
    }
    console.error('营收数据获取失败', e)
  }
}
// 请求基础统计
const loadBasicStatistics = async () => {
  try {
    const res = await getBasicStatistics()
    const list = res.rows || []
    // deviceType sensor:水质检测仪 flowmeter:流量计 pressure:压力表
    // status online:在线 offline: 离线
    for(let i = 0; i < list.length; i++) {
      const item = list[i]
      if (item.deviceType === 'sensor') {
        sensorCount.value++
        if (item.status === 'online') {
          sensorOnlineCount.value++
        } else {
          sensorOfflineCount.value++
        }
      } else if (item.deviceType === 'flowmeter') {
        flowmeterCount.value++
        if (item.status === 'online') {
          flowmeterOnlineCount.value++
        } else {
          flowmeterOfflineCount.value++
        }
      } else if (item.deviceType === 'pressuremeter') {
        pressureCount.value++
        if (item.status === 'online') {
          pressureOnlineCount.value++
        } else {
          pressureOfflineCount.value++
        }
      }
    }
    
  } catch (e) {
    console.error('基础统计获取失败', e)
  }
}

// 组件挂载后初始化
onMounted(async () => {
  await nextTick()
  await initAMap()
  // initWorkOrderChart()
  // initMonitoringChart()
  await loadStatsData()
  await loadAlertRows()
  startAlertScroll()
  onChangeOrderTab(0)
  loadBasicStatistics()
  loadWaterStationList()
  loadRevenueData()
})

onUnmounted(() => {
  if (alertScrollTimer) clearInterval(alertScrollTimer)
})
</script>
<style lang="scss" scoped>
/* 智慧运营地图全屏层 */
.map-fullscreen {
  position: absolute;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1;
}
.map-fullscreen .map {
  width: 100vw;
  height: 100vh;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
}

/* 悬浮面板样式 */
.floating-panel {
  position: absolute;
  top: 80px;
  z-index: 10;
  background: none;
  pointer-events: none;
}
.left-panel.floating-panel {
  left: 30px;
  width: 520px;
  pointer-events: none;
}
.right-panel.floating-panel {
  right: 30px;
  width: 520px;
  pointer-events: none;
}
.floating-panel .panel-section {
  pointer-events: auto;
}

/* 悬浮底部tab按钮 */
.floating-tabs {
  position: absolute;
  left: 0;
  bottom: 30px;
  width: 100vw;
  display: flex;
  justify-content: center;
  z-index: 20;
  pointer-events: none;
}
.floating-tabs .tab-button {
  pointer-events: auto;
}

.header {
  width: 100%;
  height: 60px;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 20;
}

.header-bg {
  margin: 0 auto;
  width: 1920px;
  height: 60px;
  box-sizing: border-box;
  background: url('@/assets/screen-header-bg.png') center center / cover no-repeat;
}

.title {
  margin: 0 auto;
  height: 60px;
  line-height: 60px;
  font-size: 28px;
  font-weight: bold;
  color: #fff;
  text-align: center;
}

/* 预测分析原样保留 */
.main-content {
  height: calc(100vh - 140px);
  position: relative;
}
.smart-operations, .prediction-analysis {
  display: flex;
  height: 100%;
  gap: 10px;
  padding: 0 10px;
}

.left-panel {
  width: 520px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.prediction-panel {
  width: 520px;
}
.map-fullscreen .prediction-panel {
  width: 520px;
}

.right-panel {
  width: 520px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

/* 智慧运营地图全屏，四预分析右侧区域地图 */
.map-container {
  flex: 1;
  background: rgba(26, 35, 50, 0.8);
  border: 1px solid #00d4ff;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  min-width: 0;
  min-height: 0;
}
.prediction-analysis .map-container {
  height: 100%;
  width: 0;
  flex: 1 1 0%;
  margin-left: 10px;
}
.prediction-analysis .map {
  width: 100%;
  height: 100%;
  min-height: 400px;
}
.map {
  width: 100%;
  height: 100%;
}

.panel-section {
  display: flex;
  flex-direction: column;
  padding: 15px;
  width: 527px;
  height: 298px;
  box-sizing: border-box;
  background: url('@/assets/chart-bg.png') no-repeat 0 0;
  background-size: 100% 100%;
}

.ctrl-model {
  height: auto;
  background: url('@/assets/ctrl-bg.png') no-repeat 0 0;
  background-size: 100% 100%;
}

.panel-header {
  color: #00d4ff;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
  text-align: center;
}

.header-arrows {
  display: flex;
  gap: 10px;
}

.arrow {
  color: #00d4ff;
  cursor: pointer;
}

.stats-container {
  flex: 1 1 auto;
  display: flex;
  align-items: center;
}

.stats-grid {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  align-content: space-around;
  width: 70%;
  height: 100%;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.water-icon {
  background: linear-gradient(135deg, #00d4ff, #0099cc);
}

.rate-icon {
  background: linear-gradient(135deg, #6bcf7f, #4caf50);
}

.station-icon {
  background: linear-gradient(135deg, #ffd93d, #ff9800);
}

.stat-content {
  display: flex;
  align-items: baseline;
}

.stat-label, .unit {
  font-size: 14px;
  color: #fff;
}

.stat-value {
  width: 120px;
  text-align: center;
  color: #b6e2ff;
  font-size: 22px;
  font-weight: bold;
}

.equipment-stats {
  padding: 20px;
  width: 30%;
  background: url('@/assets/equipment-bg.png') no-repeat 0 0;
  background-size: 100% 100%;
  box-sizing: border-box;
}

.equipment-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.equipment-icon {
  width: 30px;
  height: 30px;
  background: #00d4ff;
  border-radius: 4px;
}

.equipment-info {
  text-align: center;
}

.equipment-label {
  color: #fff;
  font-size: 14px;
}

.equipment-value {
  color: #00d4ff;
  font-size: 20px;
  font-weight: bold;
}

.status-bars {
  flex: 1;
}

.status-item {
  // display: flex;
  // align-items: center;
  // gap: 8px;
  margin-bottom: 14px;
}
.status-labels {
  display: flex;
  justify-content: space-between;

}
.master {
  color: #b6e2ff
}
.online {
  color: #6bcf7f;
}
.offline {
  color: #ffd93d;
}
.status-label {
  color: #fff;
  font-size: 14px;
  width: 30px;
}

.status-bar {
  flex: 1;
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  overflow: hidden;
}

.status-fill {
  height: 100%;
  border-radius: 3px;
}

.status-fill.online {
  background: #6bcf7f;
}

.status-fill.offline {
  background: #ffd93d;
}

.status-percent {
  padding: 0 8px;
  // color: #fff;
  font-size: 16px;
  width: 40px;
  text-align: right;
}


.alert-table {
  font-size: 14px;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
}
.alert-header {
  display: grid;
  grid-template-columns: 1fr 0.8fr 0.6fr 1.6fr 0.6fr;
  gap: 8px;
  color: #fff;
  margin-bottom: 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #2a3441;
  background: transparent;
  position: sticky;
  top: 0;
  z-index: 2;
}
.scroll-view {
  // flex: 1 1 0;
  overflow: hidden;
  height: calc(50px * 4); /* 4行高度，防止内容溢出 */
  position: relative;
}
.alert-body {
  will-change: transform;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  z-index: 1;
}
.alert-row {
  display: grid;
  grid-template-columns: 1fr 0.8fr 0.6fr 1.6fr 0.6fr;
  gap: 8px;
  color: #ffffff;
  height: 50px;
  line-height: 50px;
  margin-bottom: 0;
  padding: 0;
  background: transparent;
}
.alert-action {
  color: #00d4ff;
  cursor: pointer;
}

// 工单处置模块美化
.work-order-section {
  background: url('@/assets/chart-bg.png') 0 0 / cover no-repeat;
  box-sizing: border-box;
}
.work-order-tabs {
  display: flex;
  gap: 8px;
  margin: 8px 0;
  justify-content: flex-end;
}
.work-order-main {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 24px;
  flex: 1 1 auto;
}
.work-order-chart-area {
  width: 50%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
}
.work-order-chart-ring {
  width: 100%;
  height: 100%;
}
// .work-order-legend {
//   display: flex;
//   flex-direction: row;
//   gap: 18px;
//   margin-top: 10px;
//   font-size: 13px;
//   justify-content: center;
// }
// .legend-item {
//   display: flex;
//   align-items: center;
//   gap: 4px;
//   color: #b6e2ff;
// }
// .legend-dot {
//   display: inline-block;
//   width: 16px;
//   height: 8px;
//   border-radius: 4px;
//   margin-right: 2px;
// }
// .legend-dot.pending {
//   background: #2176ff;
// }
// .legend-dot.processing {
//   background: #ffd93d;
// }
// .legend-dot.completed {
//   background: #6bcf7f;
// }
.work-order-indicators {
  display: flex;
  flex-direction: column;
  gap: 18px;
  flex: 1;
  justify-content: center;
}
.work-order-indicator {
  position: relative;
  // width: 282px;
  height: 45px;
  display: flex;
  align-items: center;
  overflow: hidden;
  background: url('/src/assets/work-order-bg.png') no-repeat 0 0;
  background-size: 100% 100%;
}
.work-order-indicator-bg {
  // width: 100%;
  // height: 100%;
  object-fit: fill;
  z-index: 1;
  pointer-events: none;
}
.work-order-indicator-content {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
}
.work-order-indicator-label {
  font-size: 14px;
  color: #fff;
  font-weight: bold;
  letter-spacing: 2px;
}
.work-order-indicator-value {
  flex: 1;
  font-size: 18px;
  font-weight: bold;
  color: #b6e2ff;
  text-align: center;
  // letter-spacing: 2px;
  // text-shadow: 0 0 8px #00d4ff44;
}
.work-order-indicator-unit {
  font-size: 14px;
  color: #fff;
  margin-left: 4px;
}

.tab-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid #2a3441;
  color: #fff;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.tab-btn.active {
  background: #00d4ff;
  color: #ffffff;
  border-color: #00d4ff;
}

.work-order-legend {
  display: flex;
  justify-content: space-around;
  margin-top: 10px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-color.pending {
  background: #ff6b6b;
}

.legend-color.processing {
  background: #ffd93d;
}

.legend-color.completed {
  background: #6bcf7f;
}

.monitoring-controls {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.monitor-select {
  width: 200px;
}
:deep(.el-select__wrapper) {
  background-color: rgba(255, 255, 255, 0.1);
  box-shadow: none;
}
:deep(.el-select__placeholder) {
  color: #fff;
}

.monitor-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid #2a3441;
  color: #fff;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  margin-left: 8px;
}

.monitor-btn.active {
  background: #00d4ff;
  color: #ffffff;
  border-color: #00d4ff;
}

.revenue-tabs {
  display: flex;
  gap: 8px;
  margin-bottom: 15px;
  justify-content: end;
}

.revenue-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 25px;
}

.revenue-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 0 20px;
}

.revenue-icon {
  width: 50px;
  height: 50px;
  // border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: url('@/assets/legend-icon.png') no-repeat 0 0;
  background-size: 100% 100%;
}

// .revenue-icon.income {
//   background: linear-gradient(135deg, #6bcf7f, #4caf50);
// }

// .revenue-icon.outcome {
//   background: linear-gradient(135deg, #ff6b6b, #f44336);
// }

// .revenue-icon.profit {
//   background: linear-gradient(135deg, #00d4ff, #0099cc);
// }

// .revenue-icon.rate {
//   background: linear-gradient(135deg, #ffd93d, #ff9800);
// }

.revenue-info {
  flex: 1;
}

.revenue-label {
  color: #fff;
  font-size: 14px;
  margin-bottom: 4px;
}

.revenue-value {
  margin-right: 8px;
  color: #b6e2ff;
  font-size: 22px;
  font-weight: bold;
}

.basic-stats {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 15px;
  flex: 1 1 auto;
}

.basic-stat-row {
  display: flex;
}

.basic-stat-item {
  display: flex;
  align-items: center;
  padding: 6px 20px;
  flex: 1;
  background: url('/src/assets/basic-stat-bg.png') no-repeat 0 0;
  background-size: 100% 100%;
}

.basic-stat-icon {
  width: 35px;
  height: 35px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: url('/src/assets/legend-icon.png') no-repeat center center;
  background-size: cover;
}

// .basic-stat-icon.water-quality {
//   background: linear-gradient(135deg, #00d4ff, #0099cc);
// }

// .basic-stat-icon.flow {
//   background: linear-gradient(135deg, #6bcf7f, #4caf50);
// }

// .basic-stat-icon.pressure {
//   background: linear-gradient(135deg, #ffd93d, #ff9800);
// }

.basic-stat-info {
  flex: 1;
  display: flex;
  justify-content: space-around;

  .master, .online, .offline {
    margin-right: 8px;
    font-size: 22px;
  }
}

.basic-stat-label {
  color: #fff;
  font-size: 14px;
}

.basic-stat-values {
  display: flex;
  gap: 8px;
  align-items: center;
}

.basic-stat-values .online {
  color: #6bcf7f;
  font-weight: bold;
}

.basic-stat-values .total {
  color: #ffffff;
  font-weight: bold;
}

.basic-stat-values .offline {
  color: #ff6b6b;
  font-weight: bold;
}

</style>

